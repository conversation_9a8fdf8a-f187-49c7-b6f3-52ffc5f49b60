Locales['cs'] = {
  -- Global menus
  ['cloakroom'] = 'satna',
  ['cloak_change'] = 'stiskni ~INPUT_PICKUP~ pro prevleceni.',
  ['citizen_wear'] = 'civilni obleceni',
  ['job_wear'] = 'pracovni obleceni',
  ['bank_deposit_returned'] = 'bezpecnostni poplatek ~g~$%s~s~ ti byl vracen.',
  ['bank_deposit_taken'] = 'bezpecnostni poplatek ~r~$%s~s~ ti byl odebram.',
  ['caution_afford'] = 'nemuzes si dovolit kauci ~g~$%s~s~',
  ['foot_work'] = 'musis byt na nohach, aby jsi mohl pracovat.',
  ['next_point'] = 'bez do dalsiho bodu po dokonceni tohoto.',
  ['not_your_vehicle'] = 'nejsi ridic tohoto vozidla.',
  ['in_vehicle'] = 'musis byt ve vozidle.',
  ['wrong_point'] = 'nejsi na spravnem bodu doruceni.',
  ['max_limit'] = 'neuneses vic ~y~%s~s~',
  ['not_enough'] = 'nemas dostatek ~y~%s~s~ pro pokracovani v tomto ukolu.',
  ['spawn_veh'] = 'spawnout vozidlo',
  ['spawn_veh_button'] = 'stiksni ~INPUT_PICKUP~ pro privolani dorucovaciho voziidla.',
  ['spawn_truck_button'] = 'stiskni ~INPUT_PICKUP~ pro spawnuti trucku.',
  ['spawn_blocked'] = 'vozidlo blokuje spawn!',
  ['service_vh'] = 'opravit vozidlo',
  ['return_vh'] = 'vraceni vozidla',
  ['return_vh_button'] = 'stiskni ~INPUT_PICKUP~ pro navrat vozidla.',
  ['delivery_point'] = 'dorucovaci bod',
  ['delivery'] = 'doruceni',
  ['public_enter'] = 'stiskni ~INPUT_PICKUP~ pro vstup do budovy.',
  ['public_leave'] = 'stiskni ~INPUT_PICKUP~ pro odchod z budovy.',

  -- Lumber Jack job
  ['lj_locker_room'] = 'drevorubcova satna',
  ['lj_mapblip'] = 'vyroba dreva',
  ['lj_wood'] = 'drevo',
  ['lj_pickup'] = 'stiskni ~INPUT_PICKUP~ pro ziskani dreva.',
  ['lj_cutwood'] = 'rezani dreva',
  ['lj_cutwood_button'] = 'stiskni ~INPUT_PICKUP~ pro rezani dreva.',
  ['lj_board'] = 'prkna',
  ['lj_planks'] = 'baleni prken',
  ['lj_cutwood'] = 'rezat drevo',
  ['lj_pick_boards'] = 'stiskni ~INPUT_PICKUP~ pro ziskani prken.',
  ['lj_deliver_button'] = 'stiskni ~INPUT_PICKUP~ pro doruceni prken.',

  -- Fisherman
  ['fm_fish_locker'] = 'rybarova satna',
  ['fm_fish'] = 'ryba',
  ['fm_fish_area'] = 'rybarska oblast',
  ['fm_fish_button'] = 'stiskni ~INPUT_PICKUP~ pro rybareni.',
  ['fm_spawnboat_title'] = 'spawnout lod',
  ['fm_spawnboat'] = 'stiskni ~INPUT_PICKUP~ pro privolani lodi.',
  ['fm_boat_title'] = 'lod',
  ['fm_boat_return_title'] = 'vraceni lodi',
  ['fm_boat_return_button'] = 'stiskni ~INPUT_PICKUP~ pro vraceni lodi.',
  ['fm_deliver_fish'] = 'stiskni ~INPUT_PICKUP~ pro doruceni ryby.',

  -- Fuel
  ['f_oil_refiner'] = 'satna ropne rafinerie',
  ['f_drill_oil'] = 'ropny vrtak',
  ['f_fuel'] = 'ropa',
  ['f_drillbutton'] = 'stiskni ~INPUT_PICKUP~ pro vrtani.',
  ['f_fuel_refine'] = 'refinovat ropu',
  ['f_refine_fuel_button'] = 'stiskni ~INPUT_PICKUP~ pro refinovani.',
  ['f_fuel_mixture'] = 'mix. rafinovaneho oleje',
  ['f_gas'] = 'palivo',
  ['f_fuel_mixture_button'] = 'stiskni ~INPUT_PICKUP~ pro smichani ropy.',
  ['f_deliver_gas'] = 'doruceni paliva',
  ['f_deliver_gas_button'] = 'stiskni ~INPUT_PICKUP~ pro doruceni paliva.',

  -- Miner
  ['m_miner_locker'] = 'hornikova satna',
  ['m_rock'] = 'kamen',
  ['m_pickrocks'] = 'stiskni ~INPUT_PICKUP~ pro ziskani kamenu.',
  ['m_washrock'] = 'myti kamenu',
  ['m_rock_button'] = 'stiskni ~INPUT_PICKUP~ pro myti kamenu.',
  ['m_rock_smelting'] = 'drceni kamenu',
  ['m_copper'] = 'med',
  ['m_sell_copper'] = 'prodejce medi',
  ['m_deliver_copper'] = 'stiskni ~INPUT_PICKUP~ pro doruceni medi.',
  ['m_iron'] = 'zelezo',
  ['m_sell_iron'] = 'prodejce zeleza',
  ['m_deliver_iron'] = 'stiskni ~INPUT_PICKUP~ pro doruceni zeleza.',
  ['m_gold'] = 'zlato',
  ['m_sell_gold'] = 'zlaty prodejce',
  ['m_deliver_gold'] = 'stiskni ~INPUT_PICKUP~ pro doruceni zlata.',
  ['m_diamond'] = 'diamant',
  ['m_sell_diamond'] = 'diamantovy prodejce',
  ['m_deliver_diamond'] = 'stiskni ~INPUT_PICKUP~ pro doruceni diamantu.',
  ['m_melt_button'] = 'stiskni ~INPUT_PICKUP~ pro drceni kamenu.',

  -- Reporter
  ['reporter_name'] = 'noviny San Andreas',
  ['reporter_garage'] = 'stiskni ~INPUT_PICKUP~ pro pristup do garaze.',

  -- Slaughterer
  ['s_slaughter_locker'] = 'reznikova satna',
  ['s_hen'] = 'kurnik',
  ['s_alive_chicken'] = 'zive kure',
  ['s_catch_hen'] = 'stiskni ~INPUT_PICKUP~ pro chyceni ziveho kurete.',
  ['s_slaughtered_chicken'] = 'kure na zabaleni',
  ['s_chop_animal'] = 'stiskni ~INPUT_PICKUP~ pro nasekani kurat.',
  ['s_slaughtered'] = 'reznikuv dum',
  ['s_package'] = 'baleni',
  ['s_packagechicken'] = 'kure v zasobniku',
  ['s_unpackaged'] = 'kure k zabaleni',
  ['s_unpackaged_button'] = 'stiskni ~INPUT_PICKUP~ pro vlozeni kurete do prihradky.',
  ['s_deliver'] = 'stiskni ~INPUT_PICKUP~ pro doruceni kurat v prihradkach.',

  -- Dress Designer
  ['dd_dress_locker'] = 'satna navrhare obleceni',
  ['dd_wool'] = 'vlna',
  ['dd_pickup'] = 'stiskni ~INPUT_PICKUP~ pro ziskani vlny.',
  ['dd_fabric'] = 'latka',
  ['dd_makefabric'] = 'stiskni ~INPUT_PICKUP~ pro vyrobu latky.',
  ['dd_clothing'] = 'obleceni',
  ['dd_makeclothing'] = 'stiskni ~INPUT_PICKUP~ pro vyrobu obleceni.',
  ['dd_deliver_clothes'] = 'stiskni ~INPUT_PICKUP~ pro doruceni obleceni.',
}
