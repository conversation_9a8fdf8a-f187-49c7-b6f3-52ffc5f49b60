Locales['sv'] = {
  -- Global menus
  ['cloakroom']                 = 'Omklädningsrum',
  ['cloak_change']              = 'tryck ~INPUT_PICKUP~ för att byta kläder.',
  ['citizen_wear']              = 'civilkläder',
  ['job_wear']                  = 'arbetskläder',
  ['bank_deposit_g']              = 'en deposition på ~g~$',
  ['bank_deposit_r']            = 'en deposition på ~r~$',
  ['bank_deposit2']             = '$ ~s~was made to you after your fainting.',
  ['foot_work']                 = 'du måste vara till fots för att kunna arbeta.',
  ['next_point']                = 'gå till nästa steg efter att du har avslutat det här steget.',
  ['security_deposit']          = 'säkerheten kommer att ges ~g~$',
  ['not_your_vehicle']          = "det här är inte ditt fordon eller så måste du vara föraren.",
  ['in_vehicle']                = 'du måste vara i ett fordon.',
  ['wrong_point']               = "du är inte på rätt leveranspunkt.",
  ['max_limit']                 = 'du har det maximala antalet: ',
  ['not_enough']                = "du har inte tillräckligt",
  ['not_enough2']               = ' för att fortsätta denna uppgift.',
  ['caution_taken']             = ' ~s~togs från dig.',
  ['caution_returned']          = ' ~s~återvände till dig.',
  ['spawn_veh']                 = 'spawn vehicle',
  ['spawn_veh_button']          = 'tryck ~INPUT_PICKUP~ för att ringa leveransfordonet.',
  ['spawn_truck_button']        = 'tryck ~INPUT_PICKUP~ för att spawna bilen.',
  ['service_vh']                = 'service vehicle',
  ['return_vh']                 = 'vehicle return',
  ['return_vh_button']          = 'tryck ~INPUT_PICKUP~ för att ge tillbaka fordonet.',
  ['delivery_point']            = 'delivery point',
  ['delivery']                  = 'delivery',
  ['public_enter']              = 'tryck ~INPUT_PICKUP~ för att gå in i byggnaden.',
  ['public_leave']              = 'tryck ~INPUT_PICKUP~ för att lämna byggnaden.',

  -- Lumber Jack job
  ['lj_locker_room']            = "Skogshuggare omklädningsrum",
  ['lj_mapblip']                = 'trähög',
  ['lj_wood']                   = 'träd',
  ['lj_pickup']                 = 'tryck ~INPUT_PICKUP~ för att hämta trä.',
  ['lj_cutwood']                = 'trädfällning',
  ['lj_cutwood_button']         = 'tryck ~INPUT_PICKUP~ för att hugga trä.',
  ['lj_board']                  = 'brädor',
  ['lj_planks']                 = 'brädpaket',
  ['lj_cutwood']                = 'hugg trä',
  ['lj_pick_boards']            = 'tryck ~INPUT_PICKUP~ för att hämta brädorna.',
  ['lj_deliver_button']         = 'tryck ~INPUT_PICKUP~ för att sälja brädorna.',

  -- Fisherman
  ['fm_fish_locker']            = "Fiskarens Omklädningsrum",
  ['fm_fish']                   = 'fisk',
  ['fm_fish_area']              = 'fiskeoområde',
  ['fm_fish_button']            = 'tryck ~INPUT_PICKUP~ för att fiska.',
  ['fm_spawnboat_title']        = 'spawn boat',
  ['fm_spawnboat']              = 'tryck ~INPUT_PICKUP~ för att spawna båten.',
  ['fm_boat_title']             = 'båt',
  ['fm_boat_return_title']      = 'boat return',
  ['fm_boat_return_button']     = 'tryck ~INPUT_PICKUP~ för att återvända båten.',
  ['fm_deliver_fish']           = 'tryck ~INPUT_PICKUP~ för att sälja fisk.',

  -- Fuel
  ['f_oil_refiner']             = 'Oljeraffinaderi Omklädningsrum',
  ['f_drill_oil']               = 'borra efter olja',
  ['f_fuel']                    = 'olja',
  ['f_drillbutton']             = 'tryck ~INPUT_PICKUP~ för att borra.',
  ['f_fuel_refine']             = 'förfina oljan',
  ['f_refine_fuel_button']      = 'tryck ~INPUT_PICKUP~ för att förfina oljan',
  ['f_fuel_mixture']            = 'Blanda raffinerad olja',
  ['f_gas']                     = 'gas',
  ['f_fuel_mixture_button']     = 'tryck ~INPUT_PICKUP~ för att mixa',
  ['f_deliver_gas']             = 'deliver Gas',
  ['f_deliver_gas_button']      = "tryck ~INPUT_PICKUP~ för att sälja bensin",

  -- Miner
  ['m_miner_locker']            = "Gruvarbetarnas Omklädningsrum",
  ['m_rock']                    = 'sten',
  ['m_pickrocks']               = 'tryck ~INPUT_PICKUP~ för att hämta sten.',
  ['m_washrock']                = 'sten tvättning',
  ['m_rock_button']             = 'tryck ~INPUT_PICKUP~ för att tvätta sten.',
  ['m_rock_smelting']           = 'stensmältning',
  ['m_copper']                  = 'koppar',
  ['m_sell_copper']             = 'kopparsäljare',
  ['m_deliver_copper']          = 'tryck ~INPUT_PICKUP~ för att sälja koppar.',
  ['m_iron']                    = 'järn',
  ['m_sell_iron']               = 'järnsäljare',
  ['m_deliver_iron']            = 'tryck ~INPUT_PICKUP~ för att sälja järn.',
  ['m_gold']                    = 'guld',
  ['m_sell_gold']               = "guldsäljare",
  ['m_deliver_gold']            = 'tryck ~INPUT_PICKUP~ för att sälja guld.',
  ['m_diamond']                 = 'diamant',
  ['m_sell_diamond']            = 'diamantsäljare',
  ['m_deliver_diamond']         = 'tryck ~INPUT_PICKUP~ för att sälja diamant.',
  ['m_melt_button']             = 'tryck ~INPUT_PICKUP~ för att smälta sten.',

  -- Reporter
  ['reporter_name']             = 'Aftonbladet',
  ['reporter_garage']           = 'tryck ~INPUT_PICKUP~ för att gå ner till garaget.',

  -- Slaughterer
  ['s_slaughter_locker']        = "Slaktarens Omklädningsrum",
  ['s_hen']                     = 'hönshus',
  ['s_alive_chicken']           = 'Levande Kyckling',
  ['s_catch_hen']               = 'tryck ~INPUT_PICKUP~ för att fånga en kyckling.',
  ['s_slaughtered_chicken']     = 'chicken to be packed',
  ['s_chop_animal']             = 'tryck ~INPUT_PICKUP~ för att slakta kycklingen.',
  ['s_slaughtered']             = 'Slaktarhus',
  ['s_package']                 = 'förpackning',
  ['s_packagechicken']          = 'kyckling i förpackning',
  ['s_unpackaged']              = 'kyckling väntar på att förpackas',
  ['s_unpackaged_button']       = 'tryck ~INPUT_PICKUP~ för att förpacka kycklingen.',
  ['s_deliver']                 = 'tryck ~INPUT_PICKUP~ för att sälja kycklingsförpackningen.',

  -- Dress Designer
  ['dd_dress_locker']           = "Klädesdesigners Omklädningsrum",
  ['dd_wool']                   = 'ull',
  ['dd_pickup']                 = 'tryck ~INPUT_PICKUP~ för att ta ullet.',
  ['dd_fabric']                 = 'tyg',
  ['dd_makefabric']             = 'tryck ~INPUT_PICKUP~ för att göra tyg.',
  ['dd_clothing']               = 'kläder',
  ['dd_makeclothing']           = 'tryck ~INPUT_PICKUP~ för att hämta kläder.',
  ['dd_deliver_clothes']        = 'tryck ~INPUT_PICKUP~ för att sälja kläder.',
  }
