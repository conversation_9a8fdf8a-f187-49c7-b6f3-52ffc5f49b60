Config.Jobs.reporter = {
  
  BlipInfos = {
    Sprite = 184,
    Color = 38
  },
  
  Vehicles = {
    Truck = {
      Spawner = 1,
      Hash = "hilux1",
      Trailer = "none",
	  Livery = 0,
      HasCaution = true
    }
  },
  
  Zones = {
    CloakRoom = { -- 1
	Pos   = {x =  -147.51, y = 6289.13, z = 31.49},
      Size  = {x = 1.5, y = 1.5, z = 1.5},
      Color = {r = 0, g = 0, b = 255},
      Mark<PERSON>= 20,
      Blip  = false,
      Name  = _U('reporter_cloakroom'),
      Type  = "cloakroom",
      Hint  = _U('cloak_change'),
      GPS = {x = -138.98, y = -634.19, z = 168.82},
    },
	
	VehicleSpawner = {
		Pos   = {x = -125.13, y = 6279.33, z = 31.32},
      Size  = {x = 1.0, y = 1.0, z = 1.0},
      Color = {r = 0, g = 0, b = 255},
      Marker= 39,
      Blip  = true,
      Name  = _U('reporter_name'),
      Type  = "vehspawner",
      Spawner = 1,
      Hint  = _U('reporter_garage'),
      Caution = 2000
    },

    VehicleSpawnPoint = {
		Pos   = {x = -125.13, y = 6279.33, z = 31.32},
      Size  = {x = 1.0, y = 1.0, z = 1.0},
      Marker= -1,
      Blip  = false,
      Name  = _U('service_vh'),
      Type  = "vehspawnpt",
      Spawner = 1,
      Heading = 160.42
    },

    VehicleDeletePoint = {
		Pos   = {x =  -130.86, y = 6285.93, z = 31.35},
      Size  = {x = 2.0, y = 2.0, z = 2.0},
      Color = {r = 255, g = 0, b = 0},
      Marker= 39,
      Blip  = false,
      Name  = _U('return_vh'),
      Type  = "vehdelete",
      Hint  = _U('return_vh_button'),
      Spawner = 1,
      Caution = 2000,
      GPS = 0,
      Teleport = { x = -115.48, y = -626.05, z = 36.26 }
    }
  }
}

Citizen.CreateThread(function()
	while ESX == nil do
		Citizen.Wait(100)
	end
	
	while true do
		Citizen.Wait(10)
		if IsControlJustReleased(0,167) and PlayerData.job ~= nil and PlayerData.job.name == 'reporter' then 
			if onDuty then
				local ped = PlayerPedId()
				local swimming = IsPedSwimming(ped)
				local underwater = IsPedSwimmingUnderWater(ped)
				local isDead = IsPedDeadOrDying(ped,1)
				
				if not swimming and not underwater and not isDead  then
					showMenu()
				end
			else
				ESX.ShowNotification(_U('not_onduty'))
			end
		end
	end
	
end)

function showMenu()

	ESX.UI.Menu.Open(
		'default', GetCurrentResourceName(), 'reporter_menu',
		{
			title    = _U('reporter_menu'),
			align    = 'top-right',
			elements = {
				{label = _U('cam'),		value = 'cam'},
				{label = _U('mic'),		value = 'mic'},
				{label = _U('bmic'),	value = 'bmic'},
				--{label = _U('fine'),	value = 'fine'},
			}
		},
		function(data, menu)
			local action = data.current.value
			
			if action == 'cam' then
				TriggerEvent('Cam:ToggleCam')
				ESX.ShowNotification(_U('cam'))
			elseif action == 'mic' then
				TriggerEvent('Mic:ToggleMic')
				ESX.ShowNotification(_U('mic'))
			elseif action == 'bmic' then
				TriggerEvent('Mic:ToggleBMic')
				ESX.ShowNotification(_U('bmic'))
			elseif action == 'fine' then
				finemenu()
			end

		end,
	function(data, menu)
		menu.close()
	end)
		
end

function finemenu()
	local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
	
	if closestPlayer ~= -1 and closestDistance <= 3.0 then
		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'fine',
		{
			title    = _U('fine'),
			align    = 'top-left',
			elements = {
				{label = _U('contract'), value = 0},
				{label = _U('other'),   value = 1},
				--{label = _U('average_offense'), value = 2},
				--{label = _U('major_offense'),   value = 3}
			}
		}, function(data, menu)
			OpenFineCategoryMenu(closestPlayer, data.current.value)
		end, function(data, menu)
			menu.close()
		end)
	else
		ESX.ShowNotification(_U('no_players_nearby'))
	end
end

function OpenFineCategoryMenu(player, category)

	ESX.TriggerServerCallback('esx_policejob:getFineList', function(fines)

		local elements = {}

		for i=1, #fines, 1 do
			table.insert(elements, {
				label     = fines[i].label .. ' <span style="color: 00EE4F;">$' .. fines[i].amount .. '</span>',
				value     = fines[i].id,
				amount    = fines[i].amount,
				fineLabel = fines[i].label
			})
		end

		ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'fine_category',
		{
			title    = _U('fine'),
			align    = 'top-left',
			elements = elements,
		}, function(data, menu)

			local label  = data.current.fineLabel
			local amount = data.current.amount

			menu.close()

			TriggerServerEvent('esx_biP4sSwoRdlling:sendBill', GetPlayerServerId(player), '', _U('fine_total', label, GetPlayerName(PlayerId())), amount)

			ESX.SetTimeout(300, function()
				OpenFineCategoryMenu(player, category)
			end)

		end, function(data, menu)
			menu.close()
		end)

	end, category)

end