Locales['br'] = {
  -- Menus <PERSON>
  ['cloakroom']                 = 'Vestiário',
  ['cloak_change']              = 'pressione ~INPUT_PICKUP~ mudar de roupa.',
  ['citizen_wear']              = 'retirar uniforme',
  ['job_wear']                  = 'colocar uniforme',
  ['bank_deposit']              = 'um depósito de segurança de ~g~',
  ['bank_deposit_r']            = 'um depósito de segurança de ~g~$',
  ['bank_deposit2']             = '$ ~s~foi feito para você depois do seu desmaio.',
  ['foot_work']                 = 'você deve estar a pé para poder trabalhar.',
  ['next_point']                = 'vá para o próximo passo depois de completar este.',
  ['security_deposit']          = 'a segurança dada será ~g~$',
  ['not_your_vehicle']          = "este não é o seu veículo ou você deve ser um motorista.",
  ['in_vehicle']                = 'você deve estar em um veículo.',
  ['wrong_point']               = "você não está no ponto certo de entrega.",
  ['max_limit']                 = 'você tem o máximo: ',
  ['not_enough']                = "você não tem o suficiente",
  ['not_enough2']               = ' Para continuar esta tarefa.',
  ['caution_taken']             = ' ~s~foi tirado de você.',
  ['caution_returned']          = ' ~s~foi devolvido a você.',
  ['spawn_veh']                 = 'Veiculo Spawnado',
  ['spawn_veh_button']          = 'pressione ~INPUT_PICKUP~ chamar o veículo de entrega.',
  ['spawn_truck_button']        = 'pressione ~INPUT_PICKUP~ spawnar o caminhão.',
  ['service_vh']                = 'veículo de serviço',
  ['return_vh']                 = 'veiculo devolvido',
  ['return_vh_button']          = 'pressione ~INPUT_PICKUP~ para devolver o veiculo.',
  ['delivery_point']            = 'ponto de entrega',
  ['delivery']                  = 'Entrega',
  ['public_enter']              = 'pressione ~INPUT_PICKUP~ para entrar no prédio.',
  ['public_leave']              = 'pressione ~INPUT_PICKUP~ para ir à entrada do edifício.',

  -- Trabalho de Lenhador
  ['lj_locker_room']            = "Vestiario Lenhador",
  ['lj_mapblip']                = 'pilha de madeira',
  ['lj_wood']                   = 'madeira',
  ['lj_pickup']                 = 'pressione ~INPUT_PICKUP~ para recuperar madeira.',
  ['lj_cutwood']                = 'Corte de Madeira',
  ['lj_cutwood_button']         = 'pressione ~INPUT_PICKUP~ para cortar madeira.',
  ['lj_board']                  = 'tábua',
  ['lj_planks']                 = 'tábua embalada',
  ['lj_cutwood']                = 'madeira cortada',
  ['lj_pick_boards']            = 'pressione ~INPUT_PICKUP~ para recuperar tábuas.',
  ['lj_deliver_button']         = 'pressione ~INPUT_PICKUP~ para entregar as tábuas.',

  -- Pescador
  ['fm_fish_locker']            = "Vestiario Pescador",
  ['fm_fish']                   = 'peixe',
  ['fm_fish_area']              = 'area de pesca',
  ['fm_fish_button']            = 'pressione ~INPUT_PICKUP~ para pescar.',
  ['fm_spawnboat_title']        = 'Spawnar Barco',
  ['fm_spawnboat']              = 'pressione ~INPUT_PICKUP~ para chamar um barco.',
  ['fm_boat_title']             = 'Barco',
  ['fm_boat_return_title']      = 'devolver barco',
  ['fm_boat_return_button']     = 'Pressione ~INPUT_PICKUP~ para devolver o barco.',
  ['fm_deliver_fish']           = 'pressione ~INPUT_PICKUP~ para entregar os peixes.',

  -- Refinador
  ['f_oil_refiner']             = 'Vestiario Refinador',
  ['f_drill_oil']               = 'Broca de óleo',
  ['f_fuel']                    = 'óleo',
  ['f_drillbutton']             = 'pressione ~INPUT_PICKUP~ para perfurar.',
  ['f_fuel_refine']             = 'refinar óleo',
  ['f_refine_fuel_button']      = 'pressione ~INPUT_PICKUP~ para refinar.',
  ['f_fuel_mixture']            = 'Misturar o óleo refinado',
  ['f_gas']                     = 'gasolina',
  ['f_fuel_mixture_button']     = 'pressione ~INPUT_PICKUP~ para misturar.',
  ['f_deliver_gas']             = 'Entregar Gasolina',
  ['f_deliver_gas_button']      = "pressione ~INPUT_PICKUP~ para entregar a gasolina.",

  -- Minerador
  ['m_miner_locker']            = "Vestiario Minerador",
  ['m_rock']                    = ' Pedregulho',
  ['m_pickrocks']               = 'pressione ~INPUT_PICKUP~ para obter pedras.',
  ['m_washrock']                = ' pedras limpas ',
  ['m_rock_button']             = 'pressione ~INPUT_PICKUP~ para lavar as pedras.',
  ['m_rock_smelting']           = 'Fundição',
  ['m_copper']                  = ' cobre',
  ['m_sell_copper']             = 'vender cobre',
  ['m_deliver_copper']          = 'Pressione ~INPUT_PICKUP~ para entregar cobre.',
  ['m_iron']                    = ' ferro',
  ['m_sell_iron']               = 'vender ferro',
  ['m_deliver_iron']            = 'pressione ~INPUT_PICKUP~ para entregar ferro.',
  ['m_gold']                    = ' ouro',
  ['m_sell_gold']               = "vender ouro",
  ['m_deliver_gold']            = 'pressione ~INPUT_PICKUP~ para entregar ouro.',
  ['m_diamond']                 = ' diamante',
  ['m_sell_diamond']            = 'vender diamante',
  ['m_deliver_diamond']         = 'pressione ~INPUT_PICKUP~ para entregar diamantes.',
  ['m_melt_button']             = 'pressione ~INPUT_PICKUP~ para derreter as pedras.',

  -- Jornalista
  ['reporter_name']             = 'Globo News',
  ['reporter_garage']           = 'Pressione ~INPUT_PICKUP~ para descer à garagem.',

  -- Abatedor
  ['s_slaughter_locker']        = "Vestiario Abatedor",
  ['s_hen']                     = 'galinheiro',
  ['s_alive_chicken']           = 'galinha viva',
  ['s_catch_hen']               = 'pressione ~INPUT_PICKUP~ para pegar galinha viva.',
  ['s_slaughtered_chicken']     = 'frango para ser embalado',
  ['s_chop_animal']             = 'pressione ~INPUT_PICKUP~ para cortar as galinhas.',
  ['s_slaughtered']             = 'matadouro',
  ['s_package']                 = 'embalamento',
  ['s_packagechicken']          = 'frango na bandeija',
  ['s_unpackaged']              = 'frango para ser embalado',
  ['s_unpackaged_button']       = 'pressione ~INPUT_PICKUP~ para colocar o frango em uma bandeja.',
  ['s_deliver']                 = 'pressione ~INPUT_PICKUP~ para entregar as bandejas de frango.',

  -- Costureiro
  ['dd_dress_locker']           = "Vestiario Costureiro",
  ['dd_wool']                   = 'lã',
  ['dd_pickup']                 = 'pressione ~INPUT_PICKUP~ para obter lã.',
  ['dd_fabric']                 = 'tecido',
  ['dd_makefabric']             = 'Pressione ~INPUT_PICKUP~ para fazer tecido.',
  ['dd_clothing']               = 'roupas',
  ['dd_makeclothing']           = 'pressione ~INPUT_PICKUP~ para obter roupas.',
  ['dd_deliver_clothes']        = 'pressione ~INPUT_PICKUP~ para entregar roupas.',
}
