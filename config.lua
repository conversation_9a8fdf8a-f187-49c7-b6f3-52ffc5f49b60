Config              = {}
Config.DrawDistance = 100.0
Config.Locale       = 'en'
Config.Jobs         = {}

Config.MaxCaution = 10000 -- the max caution allowed

Config.MaxInService = -1

Config.GlitchFineCost = 500
Config.WaitingTime = 4000


Config.pas = 'ad2562KKKh27hd'

 

-- ضعف الاجر الاحترافي (: -- by {NA Store}
-- لاتعدل
Config.miner = false -- دبل معادن
Config.slaughterer = false -- دبل دواجن
Config.lumberjack = false -- دبل اخشاب
Config.tailor = false -- دبل أقمشة
Config.fueler = false -- دبل نفط
Config.farmer = false -- دبل مزارع
Config.fisherman = false -- دبل أسماك
Config.vegetables = false -- دبل شركة الخضروات
Config.bricks = false -- دبل طوب
--

Config.Prices = {
	fisherman 	= 200,
	farmer 	= 90, -- ملاحظة بالنسبة لسعر المزارع يرجة كتابة العدد كامل مثلا 230 او 240 - يمنع كتابة سعر 235 لازم يكون فيه صفر وشكرا
	turtle 	= 200,
	shark 	= 5600,
	fueler 		= 190,
	gas 		= 195,
	lumberjack 	= 270,
	miner = {
			iron 	= 135,
			copper 	= 155,
			gold 	= 880,
			diamond = 5000,
			} ,
	slaughterer = 250,
	vegetables = 110,
	tailor 		= 198,
	bricks 		= 180,
}

Config.Xp = {
	fisherman 	= 10,
	farmer 	= 14,
	turtle 	= 22,
	shark 	= 30,
	fueler 		= 18,
	gas 		= 16,
	lumberjack 	= 26,
	miner = {
			iron 	= 11,
			copper 	= 8,
			gold 	= 22,
			diamond = 50,
			} ,
	slaughterer = 22,
	vegetables = 11,
	tailor 		= 19,
	bricks 		= 11,
}

Config.clockroom = {
	[1] = 0,
	[2] = 0,
	[3] = 0,
	[4] = 0,
}


Config.jobSkiin = {
	['fisherman'] = {
		m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
		['torso_1'] = 143,   ['torso_2'] = 4,
		['decals_1'] = 0,   ['decals_2'] = 0,
		['arms'] = 4,
		['pants_1'] = 3,   ['pants_2'] = 0,
		['shoes_1'] = 1,   ['shoes_2'] = 0,
		['helmet_1'] = -1,  ['helmet_2'] = 0,
		['chain_1'] = 0,    ['chain_2'] = 0,
		['glasses_1'] = 0, 	['glasses_2'] = 0,
		['ears_1'] = 0,     ['ears_2'] = 0,
		['bproof_1'] = 0,  	['bproof_2'] = 0,
		['chain_1'] = 0,    ['chain_2'] = 0 },
		f = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
		['torso_1'] = 143,   ['torso_2'] = 4,
		['decals_1'] = 0,   ['decals_2'] = 0,
		['arms'] = 4,
		['pants_1'] = 3,   ['pants_2'] = 0,
		['shoes_1'] = 1,   ['shoes_2'] = 0,
		['helmet_1'] = -1,  ['helmet_2'] = 0,
		['chain_1'] = 0,    ['chain_2'] = 0,
		['glasses_1'] = 0, 	['glasses_2'] = 0,
		['ears_1'] = 0,     ['ears_2'] = 0,
		['bproof_1'] = 0,  	['bproof_2'] = 0,
		['chain_1'] = 0,    ['chain_2'] = 0 },
	},
	['vegetables'] = {
		m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	    ['torso_1'] = 143,   ['torso_2'] = 3,
	    ['decals_1'] = 0,   ['decals_2'] = 0,
	    ['arms'] = 4,
	    ['pants_1'] = 3,   ['pants_2'] = 0,
	    ['shoes_1'] = 1,   ['shoes_2'] = 0,
	    ['helmet_1'] = -1,  ['helmet_2'] = 0,
	    ['chain_1'] = 0,    ['chain_2'] = 0,
	    ['glasses_1'] = 0, 	['glasses_2'] = 0,
	    ['ears_1'] = 0,     ['ears_2'] = 0,
	    ['bproof_1'] = 0,  	['bproof_2'] = 0,
	    ['chain_1'] = 0,    ['chain_2'] = 0 },
		m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	    ['torso_1'] = 143,   ['torso_2'] = 3,
	    ['decals_1'] = 0,   ['decals_2'] = 0,
	    ['arms'] = 4,
	    ['pants_1'] = 3,   ['pants_2'] = 0,
	    ['shoes_1'] = 1,   ['shoes_2'] = 0,
	    ['helmet_1'] = -1,  ['helmet_2'] = 0,
	    ['chain_1'] = 0,    ['chain_2'] = 0,
	    ['glasses_1'] = 0, 	['glasses_2'] = 0,
	    ['ears_1'] = 0,     ['ears_2'] = 0,
	    ['bproof_1'] = 0,  	['bproof_2'] = 0,
	    ['chain_1'] = 0,    ['chain_2'] = 0 },
	},
	['tailor'] = {
		m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	    ['torso_1'] = 143,   ['torso_2'] = 1,
	    ['decals_1'] = 0,   ['decals_2'] = 0,
	    ['arms'] = 4,
	    ['pants_1'] = 3,   ['pants_2'] = 0,
	    ['shoes_1'] = 1,   ['shoes_2'] = 0,
	    ['helmet_1'] = -1,  ['helmet_2'] = 0,
	    ['chain_1'] = 0,    ['chain_2'] = 0,
	    ['glasses_1'] = 0, 	['glasses_2'] = 0,
	    ['ears_1'] = 0,     ['ears_2'] = 0,
	    ['bproof_1'] = 0,  	['bproof_2'] = 0,
	    ['chain_1'] = 0,    ['chain_2'] = 0 },
		f = { ['tshirt_1'] = 23,  ['tshirt_2'] = 0,
		['torso_1'] = 24,   ['torso_2'] = 1,
		['decals_1'] = 0,   ['decals_2'] = 0,
		['arms'] = 0,
		['pants_1'] = 48,   ['pants_2'] = 3,
		['shoes_1'] = 5,   ['shoes_2'] = 0,
		['helmet_1'] = -1,  ['helmet_2'] = 2,
		['chain_1'] = 0,    ['chain_2'] = 0,
		['glasses_1'] = 5, 	['glasses_2'] = 0,
		['ears_1'] = 0,     ['ears_2'] = 0,
		['bproof_1'] = 0,  	['bproof_2'] = 0,
		['chain_1'] = 0,    ['chain_2'] = 0 },
	},
	['slaughterer'] = {
	  m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 2,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
	  m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 2,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
  },
  ['lumberjack'] = {
	  m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 0,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
	  f = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 0,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
  },

  ['farmer'] = {
	  m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 3,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
	  f = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 3,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
  },

  ['reporter'] = {
	  m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 1,
	  ['torso_1'] = 99,   ['torso_2'] = 0,
	  ['decals_1'] = 2,   ['decals_2'] = 0,
	  ['arms'] = 1,
	  ['pants_1'] = 28,   ['pants_2'] = 8,
	  ['shoes_1'] = 21,   ['shoes_2'] = 0,
	  ['helmet_1'] = 7,  ['helmet_2'] = 1,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
	  f = { ['tshirt_1'] = 40,  ['tshirt_2'] = 4,
	  ['torso_1'] = 7,   ['torso_2'] = 0,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 44,
	  ['pants_1'] = 37,   ['pants_2'] = 1,
	  ['shoes_1'] = 22,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 6, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
  },

  ['cement'] = {
	  m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 65,   ['torso_2'] = 2,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 17,
	  ['pants_1'] = 38,   ['pants_2'] = 2,
	  ['shoes_1'] = 27,   ['shoes_2'] = 0,
	  ['helmet_1'] = 60,  ['helmet_2'] = 6,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
	  f = { ['tshirt_1'] = 14,  ['tshirt_2'] = 0,
	  ['torso_1'] = 59,   ['torso_2'] = 2,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 46,
	  ['pants_1'] = 38,   ['pants_2'] = 2,
	  ['shoes_1'] = 26,   ['shoes_2'] = 0,
	  ['helmet_1'] = 53,  ['helmet_2'] = 1,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 5, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
  },

  ['miner'] = {
	m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 5,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
	  f = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 5,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
  },

  ['fueler'] = {
	m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 6,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
	  f = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 143,   ['torso_2'] = 6,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 4,
	  ['pants_1'] = 3,   ['pants_2'] = 0,
	  ['shoes_1'] = 1,   ['shoes_2'] = 0,
	  ['helmet_1'] = -1,  ['helmet_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
  },

  ['bricks'] = {
	m = { ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
	  ['torso_1'] = 65,   ['torso_2'] = 3,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 17,
	  ['pants_1'] = 38,   ['pants_2'] = 3,
	  ['shoes_1'] = 27,   ['shoes_2'] = 0,
	  ['helmet_1'] = 60,  ['helmet_2'] = 7,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
	  f = { ['tshirt_1'] = 14,  ['tshirt_2'] = 0,
	  ['torso_1'] = 59,   ['torso_2'] = 3,
	  ['decals_1'] = 0,   ['decals_2'] = 0,
	  ['arms'] = 14,
	  ['pants_1'] = 34,   ['pants_2'] = 3,
	  ['shoes_1'] = 27,   ['shoes_2'] = 0,
	  ['helmet_1'] = 60,  ['helmet_2'] = 7,
	  ['chain_1'] = 0,    ['chain_2'] = 0,
	  ['glasses_1'] = 0, 	['glasses_2'] = 0,
	  ['ears_1'] = 0,     ['ears_2'] = 0,
	  ['bproof_1'] = 0,  	['bproof_2'] = 0,
	  ['chain_1'] = 0,    ['chain_2'] = 0 },
  },

}

Config.PublicZones = {

	EnterBuilding = {
		Pos   = { x = -118.21, y = -607.14, z = 35.28 },
		Size  = {x = 3.0, y = 3.0, z = 0.2},
		Color = {r = 204, g = 204, b = 0},
		Marker= 1,
		Blip  = false,
		Name  = _U('reporter_name'),
		Type  = "teleport",
		Hint  = _U('public_enter'),
		Teleport = { x = -139.09, y = -620.74, z = 167.82 }
	},

	ExitBuilding = {
		Pos   = { x = -139.45, y = -617.32, z = 167.82 },
		Size  = {x = 3.0, y = 3.0, z = 0.2},
		Color = {r = 204, g = 204, b = 0},
		Marker= 1,
		Blip  = false,
		Name  = _U('reporter_name'),
		Type  = "teleport",
		Hint  = _U('public_leave'),
		Teleport = { x = -113.07, y = -604.93, z = 35.28 },
	}

}
