Locales['es'] = {
  -- Global menus
  ['cloakroom'] = 'guardarropas',
  ['cloak_change'] = 'presiona ~INPUT_PICKUP~ para cambiarte la ropa.',
  ['citizen_wear'] = 'ropa de civil',
  ['job_wear'] = 'ropa de trabajo',
  ['bank_deposit_returned'] = 'un deposito de seguro de ~g~$%s~s~ se te devolvió.',
  ['bank_deposit_taken'] = 'un deposito de seguro de ~r~$%s~s~ se te tomó.',
  ['caution_afford'] = 'no puedes pagar el deposito de seguro de ~g~$%s~s~',
  ['foot_work'] = 'debes estar en pie para trabajar.',
  ['next_point'] = 've al siguiente punto tras terminar este.',
  ['not_your_vehicle'] = 'no eres el conductor.',
  ['in_vehicle'] = 'debes estar en un vehiculo.',
  ['wrong_point'] = 'no estas en el punto correcto de entrega.',
  ['max_limit'] = 'no puedes llevar mas ~y~%s~s~',
  ['not_enough'] = 'no tienes suficientes ~y~%s~s~ para continuar esta tarea.',
  ['spawn_veh'] = 'spawnear vehiculo',
  ['spawn_veh_button'] = 'presiona ~INPUT_PICKUP~ para pedir el vehiculo de entrega.',
  ['spawn_truck_button'] = 'presiona ~INPUT_PICKUP~ para spawnear un camion.',
  ['spawn_blocked'] = 'el punto de spawn esta bloqueado!',
  ['service_vh'] = 'vehiculo de servicio',
  ['return_vh'] = 'devolver vehiculo',
  ['return_vh_button'] = 'presiona ~INPUT_PICKUP~ para devolver vehiculo.',
  ['delivery_point'] = 'punto de entrega',
  ['delivery'] = 'entrega',
  ['public_enter'] = 'presiona ~INPUT_PICKUP~ para entrar al edificio.',
  ['public_leave'] = 'presiona ~INPUT_PICKUP~ para ir a la entrada del edificio.',

  -- Lumber Jack job
  ['lj_locker_room'] = 'guardarropas',
  ['lj_mapblip'] = 'pila de madera',
  ['lj_wood'] = 'madera',
  ['lj_pickup'] = 'presiona ~INPUT_PICKUP~ para agarrar madera.',
  ['lj_cutwood'] = 'cortar madera',
  ['lj_cutwood_button'] = 'presiona ~INPUT_PICKUP~ para cortar madera.',
  ['lj_board'] = 'tablas',
  ['lj_planks'] = 'empaquetar',
  ['lj_cutwood'] = 'cortar madera',
  ['lj_pick_boards'] = 'presiona ~INPUT_PICKUP~ para agarrar tablas.',
  ['lj_deliver_button'] = 'presiona ~INPUT_PICKUP~ para entregar tablas.',

  -- Fisherman
  ['fm_fish_locker'] = 'guardarropas',
  ['fm_fish'] = 'pescar',
  ['fm_fish_area'] = 'area de pesca',
  ['fm_fish_button'] = 'presiona ~INPUT_PICKUP~ para pescar.',
  ['fm_spawnboat_title'] = 'spawnear bote',
  ['fm_spawnboat'] = 'presiona ~INPUT_PICKUP~ para pedir un bote.',
  ['fm_boat_title'] = 'bote',
  ['fm_boat_return_title'] = 'devolver bote',
  ['fm_boat_return_button'] = 'presiona ~INPUT_PICKUP~ para devolver un bote.',
  ['fm_deliver_fish'] = 'presiona ~INPUT_PICKUP~ para entregar pescados.',

  -- Fuel
  ['f_oil_refiner'] = 'guardarropas',
  ['f_drill_oil'] = 'agujerear para encontrar petroleo',
  ['f_fuel'] = 'petroleo',
  ['f_drillbutton'] = 'presiona ~INPUT_PICKUP~ para agujerear.',
  ['f_fuel_refine'] = 'refinar petroleo',
  ['f_refine_fuel_button'] = 'presiona ~INPUT_PICKUP~ para refinar.',
  ['f_fuel_mixture'] = 'mezclar petroleo refinado',
  ['f_gas'] = 'gas',
  ['f_fuel_mixture_button'] = 'presiona ~INPUT_PICKUP~ para mezclar petroleo.',
  ['f_deliver_gas'] = 'entregar gas',
  ['f_deliver_gas_button'] = 'presiona ~INPUT_PICKUP~ para entregar gasolina.',

  -- Miner
  ['m_miner_locker'] = 'guardarropas',
  ['m_rock'] = 'roca',
  ['m_pickrocks'] = 'presiona ~INPUT_PICKUP~ para agarrar rocas.',
  ['m_washrock'] = 'lavado de rocas',
  ['m_rock_button'] = 'presiona ~INPUT_PICKUP~ para lavar rocas.',
  ['m_rock_smelting'] = 'derretir rocas',
  ['m_copper'] = 'cobre',
  ['m_sell_copper'] = 'vender cobre',
  ['m_deliver_copper'] = 'presiona ~INPUT_PICKUP~ para entregar el cobre.',
  ['m_iron'] = 'hierro',
  ['m_sell_iron'] = 'vender hierro',
  ['m_deliver_iron'] = 'presiona ~INPUT_PICKUP~ para entregar el hierro.',
  ['m_gold'] = 'oro',
  ['m_sell_gold'] = 'vender oro',
  ['m_deliver_gold'] = 'presiona ~INPUT_PICKUP~ para entregar oro.',
  ['m_diamond'] = 'diamantes',
  ['m_sell_diamond'] = 'vender diamantes',
  ['m_deliver_diamond'] = 'presiona ~INPUT_PICKUP~ para entregar los diamantes.',
  ['m_melt_button'] = 'presiona ~INPUT_PICKUP~ para derretir rocas.',

  -- Reporter
  ['reporter_name'] = 'periódico San Andreas',
  ['reporter_garage'] = 'presiona ~INPUT_PICKUP~ para ir al garage.',

  -- Slaughterer
  ['s_slaughter_locker'] = 'guardarropas',
  ['s_hen'] = 'gGallinero',
  ['s_alive_chicken'] = 'pollos vivos',
  ['s_catch_hen'] = 'presiona ~INPUT_PICKUP~ para agarrar pollos vivos.',
  ['s_slaughtered_chicken'] = 'pollo listo para empaquetar',
  ['s_chop_animal'] = 'presiona ~INPUT_PICKUP~ para cortar los pollos.',
  ['s_slaughtered'] = 'matadero',
  ['s_package'] = 'empaquetando',
  ['s_packagechicken'] = 'pollo en bandeja',
  ['s_unpackaged'] = 'pollo para empaquetar',
  ['s_unpackaged_button'] = 'presiona ~INPUT_PICKUP~ para poner el pollo en una bandeja.',
  ['s_deliver'] = 'presiona ~INPUT_PICKUP~ para entregar bandejas de pollos.',

  -- Dress Designer
  ['dd_dress_locker'] = 'guardarropas',
  ['dd_wool'] = 'lana',
  ['dd_pickup'] = 'presiona ~INPUT_PICKUP~ para agarrar lana.',
  ['dd_fabric'] = 'tela',
  ['dd_makefabric'] = 'presiona ~INPUT_PICKUP~ para hacer tela.',
  ['dd_clothing'] = 'ropa',
  ['dd_makeclothing'] = 'presiona ~INPUT_PICKUP~ para agarrar ropa.',
  ['dd_deliver_clothes'] = 'presiona ~INPUT_PICKUP~ para entregar ropa.',
}
