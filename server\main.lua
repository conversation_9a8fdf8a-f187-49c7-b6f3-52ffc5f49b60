local PlayersWorking = {}
ESX = nil

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- إضافة callback لتسجيل الدخول
ESX.RegisterServerCallback('esx_service:enableService', function(source, cb, jobName)
	-- السماح لجميع اللاعبين بتسجيل الدخول (لا توجد قيود)
	cb(true, 999, 0) -- canTakeService = true, maxInService = 999, inServiceCount = 0
end)

-- إضافة event لتسجيل الخروج
RegisterServerEvent('esx_service:disableService')
AddEventHandler('esx_service:disableService', function(jobName)
	-- لا نحتاج فعل شيء هنا، فقط للتوافق
end)

--Send the message to your discord server
function sendToDiscord (name,message,color)
  local DiscordWebHook = "https://ptb.discord.com/api/webhooks/942861711814295632/iyrC_TvTZacMUMlqyF87gblZZwa4Sp7RCs1KSHs_gZy_sbr79ofqp4u4UcqUD_nH7q6k"
  -- Modify here your discordWebHook username = name, content = message,embeds = embeds

local embeds = {
    {
        ["title"]=message,
        ["type"]="rich",
        ["color"] =color,
        ["footer"]=  { ["text"]= "الوظايف العامة", },
    }
}

  if message == nil or message == '' then return FALSE end
  PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end


local function Work(source, item)

	SetTimeout(item[1].time, function()

		if PlayersWorking[source] == true then

			local xPlayer = ESX.GetPlayerFromId(source)
			if xPlayer == nil then
				return
			end

			for i=1, #item, 1 do
				local itemQtty, requiredItemQtty = 0, 0
				if item[i].name ~= _U('delivery') then
					itemQtty = xPlayer.getInventoryItem(item[i].db_name).count
				end
				if item[1].requires ~= "nothing" then
					requiredItemQtty = xPlayer.getInventoryItem(item[1].requires).count
				end
				if item[i].name ~= _U('delivery') and itemQtty >= item[i].max then	
				TriggerClientEvent('esx:showNotification', source, _U('max_limit', item[i].name))
                PlayersWorking[source] = "no"	
				elseif item[i].requires ~= "nothing" and requiredItemQtty <= 0 then
				TriggerClientEvent('esx:showNotification', source, _U('not_enough', item[1].requires_name))
				PlayersWorking[source] = false
				else
					if item[i].name ~= _U('delivery') then
						-- Chances to drop the item
						if item[i].drop == 100 and PlayersWorking[source] ~= "no" then
							xPlayer.addInventoryItem(item[i].db_name, item[i].add)
						else
							local chanceToDrop = math.random(100)
							if chanceToDrop <= item[i].drop and PlayersWorking[source] ~= "no" then
								xPlayer.addInventoryItem(item[i].db_name, item[i].add)
							end
						end
					else
					if Config.miner and xPlayer.job.name == 'miner' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.lumberjack and xPlayer.job.name == 'lumberjack' then 
                        xPlayer.addMoney(item[i].price*2)	
					elseif Config.slaughterer and xPlayer.job.name == 'slaughterer' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.tailor and xPlayer.job.name == 'tailor' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.fueler and xPlayer.job.name == 'fueler' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.vegetables and xPlayer.job.name == 'vegetables' then 
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.farmer and xPlayer.job.name == 'farmer' then 
                        xPlayer.addMoney(item[i].price*1.8)
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5><center><b>انت كسبت: <font size=5 color=00EE4F>$<font color=white>" .. item[i].price*1.8,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft" 
				})
                Citizen.Wait(500)
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5 color=gray><center><b>نسبة الشركة: <font size=5 color=FFAE00>$<font color=white>" .. item[i].price*0.2,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft" 
				})						
					elseif xPlayer.job.name == 'farmer' and Config.double ~= 6 or Config.farmer == 1 then 
                 xPlayer.addMoney(item[i].price*0.9)	
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5><center><b>انت كسبت: <font size=5 color=00EE4F>$<font color=white>" .. item[i].price*0.9,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft" 
				})
                Citizen.Wait(500)
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5 color=gray><center><b>نسبة الشركة: <font size=5 color=FFAE00>$<font color=white>" .. item[i].price*0.1,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft" 
				})				
					elseif Config.fisherman and xPlayer.job.name == 'fisherman' then
                        xPlayer.addMoney(item[i].price*2)
					elseif Config.bricks and xPlayer.job.name == 'bricks' then
                        xPlayer.addMoney(item[i].price*2)
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5><center><b>انت كسبت: <font size=5 color=00EE4F>$<font color=white>" .. item[i].price*2,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft"
				})
                Citizen.Wait(500)
                TriggerClientEvent('pNotify:SendNotification',source, {
					text = "<font size=5 color=gray><center><b>نسبة الشركة: <font size=5 color=FFAE00>$<font color=white>" .. item[i].price*0,
					type = "alert",
					queue = left,
					timeout = 1000,
					killer = false,
					theme = "gta",
					layout = "centerLeft"
				})
					else
                        xPlayer.addMoney(item[i].price)
                    end
						--TriggerEvent('esx_xp:addXP', source , item[i].xp)	
						local mejob = xPlayer.job.label
						TriggerEvent('SvStore_xplevel:updateCurrentPlayerXP', source, 'add', item[i].xp, ' بيع منتج في وظيفة '..mejob)
						sendToDiscord((' الوظايف '), GetPlayerName(source) .. " باع  ".. item[1].requires .. " مقابل ".. item[i].price.."دولار",56108)
					end
				end
			end

			if item[1].requires ~= "nothing" then
				iTemToRemove = xPlayer.getInventoryItem(item[1].requires).count
			 if PlayersWorking[source] == true then
				if iTemToRemove > 0 then
					  xPlayer.removeInventoryItem(item[1].requires, item[1].remove)	
				end					  
			 end
			end

			Work(source, item)

		end
	end)
end

local IsHave = false

RegisterNetEvent('esx_jobs:addToData')
AddEventHandler('esx_jobs:addToData', function(plate_car)
	local xPlayer = ESX.GetPlayerFromId(source)
	MySQL.Async.execute("INSERT INTO owned_veh_job (plate, identifier) VALUES (@plate, @identifier)",
	{
		["@plate"] = plate_car,
		['@identifier'] = xPlayer.identifier
	})
end)

RegisterNetEvent('esx_jobs:delete_plate')
AddEventHandler('esx_jobs:delete_plate', function(plate_car)
	xPlayer = ESX.GetPlayerFromId(source)
	MySQL.Async.execute('DELETE FROM owned_veh_job WHERE plate = @plate AND identifier = @identifier', {
		['@plate'] = plate_car,
		['@identifier'] = xPlayer.identifier
	})
end)

RegisterServerEvent('esx_jobs:A_start_2KNK2_Work') -- startWork
AddEventHandler('esx_jobs:A_start_2KNK2_Work', function(item, pas)
    if pas == Config.pas then
	if not PlayersWorking[source] then
		PlayersWorking[source] = true
		Work(source, item)
	else
		print(('esx_jobs: %s attempted to exploit the marker!'):format(GetPlayerIdentifiers(source)[1]))
	end
	end
end)

RegisterServerEvent('esx_jobs:stopWork')
AddEventHandler('esx_jobs:stopWork', function()
	PlayersWorking[source] = false
end)

function RemoveOwnedVehicle(plate)
	MySQL.Async.execute('DELETE FROM owned_vehicles WHERE plate = @plate', {
		['@plate'] = plate
	})
end

RegisterServerEvent('esx_jobs:A_K2dRcaution_v63j') -- esx_jobs:caution
AddEventHandler('esx_jobs:A_K2dRcaution_v63j', function(pas, cautionType, cautionAmount, spawnPoint, vehicle, plate)
    if pas == Config.pas then
	local xPlayer = ESX.GetPlayerFromId(source)
	if cautionType == "take" then
		TriggerEvent('esx_addonaccount:getAccount', 'caution', xPlayer.identifier, function(account)
			xPlayer.removeAccountMoney('bank', cautionAmount)
			account.addMoney(cautionAmount)
		end)

	--	TriggerClientEvent('esx:showNotification', source, _U('bank_deposit_taken', ESX.Math.GroupDigits(cautionAmount)))
                TriggerClientEvent('pNotify:SendNotification',source, {
                    text = _U('bank_deposit_taken'), 
                    type = "info", 
                    timeout = 10000, 
                    layout = "centerLeft"
                })		
		TriggerClientEvent('esx_jobs:spawnJobVehicle', source, spawnPoint, vehicle)
	elseif cautionType == "give_back" then

		if cautionAmount > 1 then
			print(('esx_jobs: %s is using cheat engine!'):format(xPlayer.identifier))
			return
		end

		TriggerEvent('esx_addonaccount:getAccount', 'caution', xPlayer.identifier, function(account)
			local caution = account.money
			local toGive = ESX.Math.Round(caution * cautionAmount)

			xPlayer.addAccountMoney('bank', toGive)
			RemoveOwnedVehicle(plate)
			account.removeMoney(toGive)
                TriggerClientEvent('pNotify:SendNotification',source, {
                    text = _U('bank_deposit_returned'), 
                    type = "info", 
                    timeout = 10000, 
                    layout = "centerLeft"
                })					
			--TriggerClientEvent('esx:showNotification', source, _U('bank_deposit_returned', ESX.Math.GroupDigits(toGive)))
		end)
	end
	end
end)

function GetCharacterName(source)
	local xPlayer = ESX.GetPlayerFromId(source)
	
	if xPlayer then
		if true then
			return xPlayer.getName()
		end
	else
		return GetPlayerName(source)
	end
end

function doublemoney_miner(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.miner then
    Config.miner = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'miner', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'miner', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر المعادن", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.miner = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'miner', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'miner', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر المعادن", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

function doublemoney_slaughterer(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.slaughterer then
    Config.slaughterer = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'slaughterer', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'slaughterer', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر الدواجن", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.slaughterer = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'slaughterer', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'slaughterer', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر الدواجن", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

function doublemoney_lumberjack(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.lumberjack then
    Config.lumberjack = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'lumberjack', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'lumberjack', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر الأخشاب", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.lumberjack = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'lumberjack', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'lumberjack', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر الأخشاب", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

function doublemoney_tailor(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.tailor then
    Config.tailor = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'tailor', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'tailor', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر الأقمشة", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.tailor = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'tailor', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'tailor', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر الأقمشة", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

function doublemoney_fueler(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.fueler then
    Config.fueler = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'fueler', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'fueler', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر النفط و الغاز", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.fueler = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'fueler', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'fueler', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر النفط و الغاز", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

function doublemoney_farmer(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.farmer then
    Config.farmer = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'farmer', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'farmer', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر المزارع", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.farmer = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'farmer', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'farmer', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر المزارع", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

function doublemoney_fisherman(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.fisherman then
    Config.fisherman = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'fisherman', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'fisherman', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر الأسماك", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.fisherman = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'fisherman', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'fisherman', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر الاسماك", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

function doublemoney_vegetables(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.vegetables then
    Config.vegetables = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'vegetables', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'vegetables', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر شركة الخضروات", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.vegetables = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'vegetables', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'vegetables', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر شركة الخضروات", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

function doublemoney_bricks(name, steamIdentifiers, discordIdentifiers, ingamename)
	if not Config.bricks then
    Config.bricks = true
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'bricks', true)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'bricks', true)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"بدأ ضعف أجر الأسمنت", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",********)
	else
	Config.bricks = false
	TriggerClientEvent("esx_misc:watermark_promotion", -1, 'bricks', false)
	TriggerClientEvent("failchawy:client:setmoneymultiple_cl_kjad37aj", -1, 'bricks', false)
	TriggerEvent('Mina:dlpayLogadj38', (' ضعف الأجر '),"إنتهاء ضعف أجر الأسمنت", "*من قبل المراقب* \n\nSteam: `".. name.."` \n "..steamIdentifiers.." \n "..discordIdentifiers.." \nInGame name: `"..ingamename.."`",15158332)
end
end

RegisterServerEvent("esx_jobs:togglePromotion_duble") -- ضعف الأجر
AddEventHandler("esx_jobs:togglePromotion_duble", function(job)
    local xPlayer = ESX.GetPlayerFromId(source)
    local name = GetPlayerName(source)
    local steamIdentifiers = GetPlayerIdentifiers(source)[1]
    local discordIdentifiers = GetPlayerIdentifiers(source)[5]
    local ingamename = xPlayer.getName()
	if xPlayer.job.name == 'admin' and xPlayer.getGroup() == "superadmin" or xPlayer.getGroup() == "admin" or xPlayer.getGroup() == "aplus" or xPlayer.getGroup() == "a" or xPlayer.getGroup() == "modplus" then
    if job == 'miner' then
	doublemoney_miner(name, steamIdentifiers, discordIdentifiers, ingamename)
	elseif job == 'slaughterer' then
	doublemoney_slaughterer(name, steamIdentifiers, discordIdentifiers, ingamename)
	elseif job == 'lumberjack' then
	doublemoney_lumberjack(name, steamIdentifiers, discordIdentifiers, ingamename)
	elseif job == 'tailor' then
	doublemoney_tailor(name, steamIdentifiers, discordIdentifiers, ingamename)
	elseif job == 'fueler' then
	doublemoney_fueler(name, steamIdentifiers, discordIdentifiers, ingamename)
	elseif job == 'farmer' then
	doublemoney_farmer(name, steamIdentifiers, discordIdentifiers, ingamename)
	elseif job == 'fisherman' then
	doublemoney_fisherman(name, steamIdentifiers, discordIdentifiers, ingamename)
	elseif job == 'vegetables' then
	doublemoney_vegetables(name, steamIdentifiers, discordIdentifiers, ingamename)
	elseif job == 'bricks' then
	doublemoney_bricks(name, steamIdentifiers, discordIdentifiers, ingamename)
  end
  else
  print(('esx_jobs: %s attempted to toggle Promotion (not admin!)!'):format(xPlayer.identifier))
  end
end)

RegisterServerEvent('esx_misc:GetCacheJobsW')
AddEventHandler('esx_misc:GetCacheJobsW', function()
    local xPlayer = ESX.GetPlayerFromId(source)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'miner', Config.miner)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'slaughterer', Config.slaughterer)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'lumberjack', Config.slaughterer)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'tailor', Config.tailor)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'fueler', Config.fueler)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'farmer', Config.farmer)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'fisherman', Config.fisherman)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'vegetables', Config.vegetables)
    TriggerClientEvent("esx_misc:updatePromotionStatus", source, 'bricks', Config.bricks)
end)